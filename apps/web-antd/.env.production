VITE_BASE=/

# 请求路径
VITE_BASE_URL=http://127.0.0.1:48080
# 接口地址
VITE_GLOB_API_URL=http://127.0.0.1:48080/admin-api
# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server

# 是否开启压缩，可以设置为 none, brotli, gzip
VITE_COMPRESS=none

# 是否开启 PWA
VITE_PWA=false

# vue-router 的模式
VITE_ROUTER_HISTORY=hash

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 打包后是否生成dist.zip
VITE_ARCHIVER=true

# 生产环境代理配置（通常生产环境不需要代理，直接使用 VITE_GLOB_API_URL）
# 如果需要代理，可以配置：
# VITE_PROXY=[["/admin-api","http://your-production-server.com/admin-api"]]
