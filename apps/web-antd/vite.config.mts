/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-07-19 17:25:51
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 09:27:47
 */
import { createProxyFromEnv, defineConfig, loadEnv } from '@vben/vite-config';

export default defineConfig(async (config) => {
  const { mode } = config;
  const root = process.cwd();

  // 加载环境变量
  const env = await loadEnv('VITE_', [
    '.env',
    '.env.local',
    `.env.${mode}`,
    `.env.${mode}.local`,
  ]);

  // 从环境变量创建代理配置
  const proxy = createProxyFromEnv(env);

  return {
    application: {},
    vite: {
      server: {
        proxy,
      },
    },
  };
});
